import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/category_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/draft_controller.dart';
import '../controllers/poll_controller.dart';

class WalletBinding extends Bindings {
  @override
  void dependencies() {
    // Register WalletController as a singleton
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Register all core controllers that should be available app-wide
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class CoreBinding extends Bindings {
  @override
  void dependencies() {
    // Register only non-authentication dependent controllers
    // Authentication-dependent controllers will be initialized in MainNavigationScreen
    Get.put<DraftController>(DraftController(),
        permanent: true); // Uses local storage only
  }
}

class AuthenticatedBinding extends Bindings {
  @override
  void dependencies() {
    // Register authentication-dependent controllers
    // These should only be initialized after user authentication
    Get.put<CategoryController>(CategoryController(), permanent: true);
    Get.put<WalletController>(WalletController(), permanent: true);
    Get.put<PostController>(PostController(), permanent: true);
    Get.put<PollController>(PollController(), permanent: true);
  }
}
