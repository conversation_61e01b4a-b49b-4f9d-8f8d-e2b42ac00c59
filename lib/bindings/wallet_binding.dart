import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../controllers/category_controller.dart';
import '../controllers/post_controller.dart';
import '../controllers/draft_controller.dart';
import '../controllers/poll_controller.dart';

class WalletBinding extends Bindings {
  @override
  void dependencies() {
    // Register WalletController as a singleton
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Register all core controllers that should be available app-wide
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class CoreBinding extends Bindings {
  @override
  void dependencies() {
    // Register all essential controllers that might be needed before authentication
    Get.put<CategoryController>(CategoryController(), permanent: true);
    Get.put<WalletController>(WalletController(), permanent: true);
    Get.put<PostController>(PostController(), permanent: true);
    Get.put<DraftController>(DraftController(), permanent: true);
    Get.put<PollController>(<PERSON><PERSON><PERSON>roller(), permanent: true);
  }
}
